import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';

import { DailyGoalSettings } from '../DailyGoalSettings';

// Mock the useDailyGoals hook
jest.mock('../../../../hooks/useDailyGoals');

import { useDailyGoals } from '../../../../hooks/useDailyGoals';

const mockUseDailyGoals = useDailyGoals as jest.MockedFunction<typeof useDailyGoals>;

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const theme = createTheme({
    palette: {
      mode: 'dark',
    },
  });

  return (
    <ThemeProvider theme={theme}>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        {children}
      </LocalizationProvider>
    </ThemeProvider>
  );
};

describe('DailyGoalSettings', () => {
  const mockUpdateDailyGoal = jest.fn();
  const mockEnableDailyGoal = jest.fn();
  const mockOnSuccess = jest.fn();
  const mockOnError = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();

    mockUseDailyGoals.mockReturnValue({
      currentGoal: {
        targetAmount: 100,
        currency: 'USD',
        isEnabled: false,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
      updateDailyGoal: mockUpdateDailyGoal,
      enableDailyGoal: mockEnableDailyGoal,
      deleteGoal: jest.fn(),
      getGoalProgress: jest.fn(),
      recordAchievement: jest.fn(),
    });
  });

  describe('Rendering', () => {
    it('should render the component with correct title and description', () => {
      render(<DailyGoalSettings onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      expect(screen.getByText('Daily Earnings Goal')).toBeInTheDocument();
      expect(screen.getByText(/Set a daily earnings target to track your progress/)).toBeInTheDocument();
    });

    it('should render the enable toggle', () => {
      render(<DailyGoalSettings onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      expect(screen.getByRole('checkbox', { name: /enable daily earnings goal/i })).toBeInTheDocument();
    });

    it('should render the target amount input', () => {
      render(<DailyGoalSettings onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      expect(screen.getByLabelText(/daily target amount/i)).toBeInTheDocument();
    });

    it('should render the currency selector', () => {
      render(<DailyGoalSettings onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      expect(screen.getByLabelText(/currency/i)).toBeInTheDocument();
    });

    it('should render the save button', () => {
      render(<DailyGoalSettings onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      expect(screen.getByText('Save Goal')).toBeInTheDocument();
    });
  });

  describe('Goal State Management', () => {
    it('should initialize form fields from currentGoal', () => {
      render(<DailyGoalSettings onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      const targetInput = screen.getByDisplayValue('100');
      const currencySelect = screen.getByDisplayValue('USD ($)');
      const enableToggle = screen.getByRole('checkbox', { name: /enable daily earnings goal/i });

      expect(targetInput).toBeInTheDocument();
      expect(currencySelect).toBeInTheDocument();
      expect(enableToggle).not.toBeChecked();
    });

    it('should handle enabled goal state', () => {
      mockUseDailyGoals.mockReturnValue({
        currentGoal: {
          targetAmount: 150,
          currency: 'EUR',
          isEnabled: true,
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01',
        },
        updateDailyGoal: mockUpdateDailyGoal,
        enableDailyGoal: mockEnableDailyGoal,
        deleteGoal: jest.fn(),
        getGoalProgress: jest.fn(),
        recordAchievement: jest.fn(),
      });

      render(<DailyGoalSettings onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      const targetInput = screen.getByDisplayValue('150');
      const currencySelect = screen.getByDisplayValue('EUR (€)');
      const enableToggle = screen.getByRole('checkbox', { name: /enable daily earnings goal/i });

      expect(targetInput).toBeInTheDocument();
      expect(currencySelect).toBeInTheDocument();
      expect(enableToggle).toBeChecked();
    });

    it('should handle null currentGoal', () => {
      mockUseDailyGoals.mockReturnValue({
        currentGoal: null,
        updateDailyGoal: mockUpdateDailyGoal,
        enableDailyGoal: mockEnableDailyGoal,
        deleteGoal: jest.fn(),
        getGoalProgress: jest.fn(),
        recordAchievement: jest.fn(),
      });

      render(<DailyGoalSettings onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      const targetInput = screen.getByLabelText(/daily target amount/i);
      const currencySelect = screen.getByDisplayValue('USD ($)');
      const enableToggle = screen.getByRole('checkbox', { name: /enable daily earnings goal/i });

      expect(targetInput).toHaveValue('');
      expect(currencySelect).toBeInTheDocument();
      expect(enableToggle).not.toBeChecked();
    });
  });

  describe('User Interactions', () => {
    it('should call enableDailyGoal when toggle is clicked', async () => {
      const user = userEvent.setup();
      render(<DailyGoalSettings onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      const toggle = screen.getByRole('checkbox', { name: /enable daily earnings goal/i });
      await user.click(toggle);

      expect(mockEnableDailyGoal).toHaveBeenCalledWith(true);
    });

    it('should update target amount when input changes', async () => {
      const user = userEvent.setup();
      render(<DailyGoalSettings onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      const targetInput = screen.getByLabelText(/daily target amount/i);
      await user.clear(targetInput);
      await user.type(targetInput, '200');

      expect(targetInput).toHaveValue('200');
    });

    it('should update currency when selector changes', async () => {
      const user = userEvent.setup();
      render(<DailyGoalSettings onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      const currencySelect = screen.getByLabelText(/currency/i);
      await user.click(currencySelect);

      const eurOption = screen.getByText('EUR (€)');
      await user.click(eurOption);

      expect(screen.getByDisplayValue('EUR (€)')).toBeInTheDocument();
    });

    it('should call updateDailyGoal when save button is clicked', async () => {
      const user = userEvent.setup();
      mockUpdateDailyGoal.mockResolvedValue(undefined);

      render(<DailyGoalSettings onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      const saveButton = screen.getByText('Save Goal');
      await user.click(saveButton);

      expect(mockUpdateDailyGoal).toHaveBeenCalledWith({
        targetAmount: 100,
        currency: 'USD',
        isEnabled: false,
      });
    });

    it('should call onSuccess when goal is saved successfully', async () => {
      const user = userEvent.setup();
      mockUpdateDailyGoal.mockResolvedValue(undefined);

      render(<DailyGoalSettings onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      const saveButton = screen.getByText('Save Goal');
      await user.click(saveButton);

      await waitFor(() => {
        expect(mockOnSuccess).toHaveBeenCalled();
      });
    });
  });

  describe('Error Handling', () => {
    it('should show error for invalid target amount', async () => {
      const user = userEvent.setup();
      render(<DailyGoalSettings onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      const targetInput = screen.getByLabelText(/daily target amount/i);
      await user.clear(targetInput);
      await user.type(targetInput, 'invalid');

      const saveButton = screen.getByText('Save Goal');
      await user.click(saveButton);

      await waitFor(() => {
        expect(screen.getByText('Please enter a valid target amount')).toBeInTheDocument();
      });

      expect(mockOnError).toHaveBeenCalledWith('Please enter a valid target amount');
    });

    it('should show error for negative target amount', async () => {
      const user = userEvent.setup();
      render(<DailyGoalSettings onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      const targetInput = screen.getByLabelText(/daily target amount/i);
      await user.clear(targetInput);
      await user.type(targetInput, '-50');

      const saveButton = screen.getByText('Save Goal');
      await user.click(saveButton);

      await waitFor(() => {
        expect(screen.getByText('Please enter a valid target amount')).toBeInTheDocument();
      });

      expect(mockOnError).toHaveBeenCalledWith('Please enter a valid target amount');
    });

    it('should handle updateDailyGoal errors', async () => {
      const user = userEvent.setup();
      mockUpdateDailyGoal.mockRejectedValue(new Error('Save failed'));

      render(<DailyGoalSettings onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      const saveButton = screen.getByText('Save Goal');
      await user.click(saveButton);

      await waitFor(() => {
        expect(screen.getByText('Failed to save goal. Please try again.')).toBeInTheDocument();
      });

      expect(mockOnError).toHaveBeenCalledWith('Failed to save goal. Please try again.');
    });

    it('should handle enableDailyGoal errors', async () => {
      const user = userEvent.setup();
      mockEnableDailyGoal.mockRejectedValue(new Error('Enable failed'));

      render(<DailyGoalSettings onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      const toggle = screen.getByRole('checkbox', { name: /enable daily earnings goal/i });
      await user.click(toggle);

      await waitFor(() => {
        expect(screen.getByText('Failed to update goal status. Please try again.')).toBeInTheDocument();
      });

      expect(mockOnError).toHaveBeenCalledWith('Failed to update goal status. Please try again.');
    });
  });

  describe('Form Validation', () => {
    it('should disable save button when goal is disabled', () => {
      render(<DailyGoalSettings onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      const saveButton = screen.getByText('Save Goal');
      expect(saveButton).toBeDisabled();
    });

    it('should disable save button when target amount is empty', () => {
      mockUseDailyGoals.mockReturnValue({
        currentGoal: {
          targetAmount: 0,
          currency: 'USD',
          isEnabled: true,
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01',
        },
        updateDailyGoal: mockUpdateDailyGoal,
        enableDailyGoal: mockEnableDailyGoal,
        deleteGoal: jest.fn(),
        getGoalProgress: jest.fn(),
        recordAchievement: jest.fn(),
      });

      render(<DailyGoalSettings onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      const saveButton = screen.getByText('Save Goal');
      expect(saveButton).toBeDisabled();
    });

    it('should enable save button when goal is enabled and has target amount', async () => {
      const user = userEvent.setup();
      render(<DailyGoalSettings onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      // Enable the goal
      const toggle = screen.getByRole('checkbox', { name: /enable daily earnings goal/i });
      await user.click(toggle);

      const saveButton = screen.getByText('Save Goal');
      expect(saveButton).not.toBeDisabled();
    });
  });

  describe('Success Messages', () => {
    it('should show success message after successful save', async () => {
      const user = userEvent.setup();
      mockUpdateDailyGoal.mockResolvedValue(undefined);

      render(<DailyGoalSettings onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      const saveButton = screen.getByText('Save Goal');
      await user.click(saveButton);

      await waitFor(() => {
        expect(screen.getByText('Daily earnings goal saved successfully!')).toBeInTheDocument();
      });
    });
  });
});
