import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeProvider, createTheme } from '@mui/material/styles';

import { BackupSettingsSection } from '../BackupSettingsSection';

// Mock the useBackupSettings hook
jest.mock('../../../../hooks/useBackupSettings');

import { useBackupSettings } from '../../../../hooks/useBackupSettings';

const mockUseBackupSettings = useBackupSettings as jest.MockedFunction<typeof useBackupSettings>;

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const theme = createTheme({
    palette: {
      mode: 'dark',
    },
  });

  return (
    <ThemeProvider theme={theme}>
      {children}
    </ThemeProvider>
  );
};

describe('BackupSettingsSection', () => {
  const mockToggleBackupEnabled = jest.fn();
  const mockUpdateBackupFrequency = jest.fn();
  const mockUpdateBackupPath = jest.fn();
  const mockUpdateMaxBackups = jest.fn();
  const mockSelectBackupDirectory = jest.fn();
  const mockPerformManualBackup = jest.fn();
  const mockOnSuccess = jest.fn();
  const mockOnError = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();

    mockUseBackupSettings.mockReturnValue({
      config: {
        enabled: false,
        frequency: 'daily' as const,
        backupPath: '/test/path',
        maxBackups: 10,
      },
      status: {
        isRunning: false,
        nextScheduledBackup: null,
        lastBackupTime: undefined,
        lastBackupSuccess: undefined,
        lastBackupError: undefined,
      },
      toggleEnabled: mockToggleBackupEnabled,
      updateFrequency: mockUpdateBackupFrequency,
      updateBackupPath: mockUpdateBackupPath,
      updateMaxBackups: mockUpdateMaxBackups,
      selectBackupDirectory: mockSelectBackupDirectory,
      performManualBackup: mockPerformManualBackup,
      getStatusSummary: () => 'Automatic backups are disabled',
      updateConfig: jest.fn(),
      validateBackupPath: jest.fn(),
      resetConfig: jest.fn(),
      checkBackupDue: jest.fn(),
      performAutomaticBackupIfDue: jest.fn(),
    });
  });

  describe('Rendering', () => {
    it('should render the component with correct title and description', () => {
      render(<BackupSettingsSection onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      expect(screen.getByText('Automatic Backups')).toBeInTheDocument();
      expect(screen.getByText(/Configure automatic periodic backups/)).toBeInTheDocument();
    });

    it('should render the enable toggle', () => {
      render(<BackupSettingsSection onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      expect(screen.getByRole('checkbox', { name: /enable automatic backups/i })).toBeInTheDocument();
    });

    it('should render status chip', () => {
      render(<BackupSettingsSection onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      expect(screen.getByText('Automatic backups are disabled')).toBeInTheDocument();
    });

    it('should not show configuration when disabled', () => {
      render(<BackupSettingsSection onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      expect(screen.queryByText('Backup Directory')).not.toBeInTheDocument();
      expect(screen.queryByText('Browse')).not.toBeInTheDocument();
    });
  });

  describe('Enabled State', () => {
    beforeEach(() => {
      mockUseBackupSettings.mockReturnValue({
        config: {
          enabled: true,
          frequency: 'weekly' as const,
          backupPath: '/enabled/path',
          maxBackups: 15,
        },
        status: {
          isRunning: false,
          nextScheduledBackup: null,
          lastBackupTime: undefined,
          lastBackupSuccess: true,
          lastBackupError: undefined,
        },
        toggleEnabled: mockToggleBackupEnabled,
        updateFrequency: mockUpdateBackupFrequency,
        updateBackupPath: mockUpdateBackupPath,
        updateMaxBackups: mockUpdateMaxBackups,
        selectBackupDirectory: mockSelectBackupDirectory,
        performManualBackup: mockPerformManualBackup,
        getStatusSummary: () => 'Last backup successful',
        updateConfig: jest.fn(),
        validateBackupPath: jest.fn(),
        resetConfig: jest.fn(),
        checkBackupDue: jest.fn(),
        performAutomaticBackupIfDue: jest.fn(),
      });
    });

    it('should show configuration when enabled', () => {
      render(<BackupSettingsSection onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      expect(screen.getByText('Backup Directory')).toBeInTheDocument();
      expect(screen.getByText('Browse')).toBeInTheDocument();
      expect(screen.getByDisplayValue('/enabled/path')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Weekly')).toBeInTheDocument();
      expect(screen.getByDisplayValue('15')).toBeInTheDocument();
      expect(screen.getByText('Backup Now')).toBeInTheDocument();
    });

    it('should show correct status', () => {
      render(<BackupSettingsSection onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      expect(screen.getByText('Last backup successful')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('should call toggleEnabled when toggle is clicked', async () => {
      const user = userEvent.setup();
      render(<BackupSettingsSection onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      const toggle = screen.getByRole('checkbox', { name: /enable automatic backups/i });
      await user.click(toggle);

      expect(mockToggleBackupEnabled).toHaveBeenCalled();
    });

    it('should call selectBackupDirectory when browse button is clicked', async () => {
      const user = userEvent.setup();
      mockSelectBackupDirectory.mockResolvedValue('/new/path');
      
      // Set enabled state
      mockUseBackupSettings.mockReturnValue({
        config: {
          enabled: true,
          frequency: 'daily' as const,
          backupPath: '/test/path',
          maxBackups: 10,
        },
        status: {
          isRunning: false,
          nextScheduledBackup: null,
          lastBackupTime: undefined,
          lastBackupSuccess: undefined,
          lastBackupError: undefined,
        },
        toggleEnabled: mockToggleBackupEnabled,
        updateFrequency: mockUpdateBackupFrequency,
        updateBackupPath: mockUpdateBackupPath,
        updateMaxBackups: mockUpdateMaxBackups,
        selectBackupDirectory: mockSelectBackupDirectory,
        performManualBackup: mockPerformManualBackup,
        getStatusSummary: () => 'Enabled',
        updateConfig: jest.fn(),
        validateBackupPath: jest.fn(),
        resetConfig: jest.fn(),
        checkBackupDue: jest.fn(),
        performAutomaticBackupIfDue: jest.fn(),
      });

      render(<BackupSettingsSection onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      const browseButton = screen.getByText('Browse');
      await user.click(browseButton);

      expect(mockSelectBackupDirectory).toHaveBeenCalled();
      expect(mockUpdateBackupPath).toHaveBeenCalledWith('/new/path');
    });

    it('should call updateBackupFrequency when frequency changes', async () => {
      const user = userEvent.setup();
      
      // Set enabled state
      mockUseBackupSettings.mockReturnValue({
        config: {
          enabled: true,
          frequency: 'daily' as const,
          backupPath: '/test/path',
          maxBackups: 10,
        },
        status: {
          isRunning: false,
          nextScheduledBackup: null,
          lastBackupTime: undefined,
          lastBackupSuccess: undefined,
          lastBackupError: undefined,
        },
        toggleEnabled: mockToggleBackupEnabled,
        updateFrequency: mockUpdateBackupFrequency,
        updateBackupPath: mockUpdateBackupPath,
        updateMaxBackups: mockUpdateMaxBackups,
        selectBackupDirectory: mockSelectBackupDirectory,
        performManualBackup: mockPerformManualBackup,
        getStatusSummary: () => 'Enabled',
        updateConfig: jest.fn(),
        validateBackupPath: jest.fn(),
        resetConfig: jest.fn(),
        checkBackupDue: jest.fn(),
        performAutomaticBackupIfDue: jest.fn(),
      });

      render(<BackupSettingsSection onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      const frequencySelect = screen.getByDisplayValue('Daily');
      await user.click(frequencySelect);

      const weeklyOption = screen.getByText('Weekly');
      await user.click(weeklyOption);

      expect(mockUpdateBackupFrequency).toHaveBeenCalledWith('weekly');
    });

    it('should call updateMaxBackups when max backups changes', async () => {
      const user = userEvent.setup();
      
      // Set enabled state
      mockUseBackupSettings.mockReturnValue({
        config: {
          enabled: true,
          frequency: 'daily' as const,
          backupPath: '/test/path',
          maxBackups: 10,
        },
        status: {
          isRunning: false,
          nextScheduledBackup: null,
          lastBackupTime: undefined,
          lastBackupSuccess: undefined,
          lastBackupError: undefined,
        },
        toggleEnabled: mockToggleBackupEnabled,
        updateFrequency: mockUpdateBackupFrequency,
        updateBackupPath: mockUpdateBackupPath,
        updateMaxBackups: mockUpdateMaxBackups,
        selectBackupDirectory: mockSelectBackupDirectory,
        performManualBackup: mockPerformManualBackup,
        getStatusSummary: () => 'Enabled',
        updateConfig: jest.fn(),
        validateBackupPath: jest.fn(),
        resetConfig: jest.fn(),
        checkBackupDue: jest.fn(),
        performAutomaticBackupIfDue: jest.fn(),
      });

      render(<BackupSettingsSection onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      const maxBackupsInput = screen.getByDisplayValue('10');
      await user.clear(maxBackupsInput);
      await user.type(maxBackupsInput, '20');

      expect(mockUpdateMaxBackups).toHaveBeenCalledWith(20);
    });

    it('should call performManualBackup when backup now button is clicked', async () => {
      const user = userEvent.setup();
      mockPerformManualBackup.mockResolvedValue({ success: true });
      
      // Set enabled state
      mockUseBackupSettings.mockReturnValue({
        config: {
          enabled: true,
          frequency: 'daily' as const,
          backupPath: '/test/path',
          maxBackups: 10,
        },
        status: {
          isRunning: false,
          nextScheduledBackup: null,
          lastBackupTime: undefined,
          lastBackupSuccess: undefined,
          lastBackupError: undefined,
        },
        toggleEnabled: mockToggleBackupEnabled,
        updateFrequency: mockUpdateBackupFrequency,
        updateBackupPath: mockUpdateBackupPath,
        updateMaxBackups: mockUpdateMaxBackups,
        selectBackupDirectory: mockSelectBackupDirectory,
        performManualBackup: mockPerformManualBackup,
        getStatusSummary: () => 'Enabled',
        updateConfig: jest.fn(),
        validateBackupPath: jest.fn(),
        resetConfig: jest.fn(),
        checkBackupDue: jest.fn(),
        performAutomaticBackupIfDue: jest.fn(),
      });

      render(<BackupSettingsSection onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      const backupButton = screen.getByText('Backup Now');
      await user.click(backupButton);

      expect(mockPerformManualBackup).toHaveBeenCalled();
      
      await waitFor(() => {
        expect(mockOnSuccess).toHaveBeenCalled();
      });
    });
  });

  describe('Loading States', () => {
    it('should show loading state during backup', () => {
      mockUseBackupSettings.mockReturnValue({
        config: {
          enabled: true,
          frequency: 'daily' as const,
          backupPath: '/test/path',
          maxBackups: 10,
        },
        status: {
          isRunning: true,
          nextScheduledBackup: null,
          lastBackupTime: undefined,
          lastBackupSuccess: undefined,
          lastBackupError: undefined,
        },
        toggleEnabled: mockToggleBackupEnabled,
        updateFrequency: mockUpdateBackupFrequency,
        updateBackupPath: mockUpdateBackupPath,
        updateMaxBackups: mockUpdateMaxBackups,
        selectBackupDirectory: mockSelectBackupDirectory,
        performManualBackup: mockPerformManualBackup,
        getStatusSummary: () => 'Creating backup...',
        updateConfig: jest.fn(),
        validateBackupPath: jest.fn(),
        resetConfig: jest.fn(),
        checkBackupDue: jest.fn(),
        performAutomaticBackupIfDue: jest.fn(),
      });

      render(<BackupSettingsSection onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      expect(screen.getByText('Creating Backup...')).toBeInTheDocument();
      expect(screen.getByText('Creating backup...')).toBeInTheDocument(); // Status chip
    });

    it('should disable backup button when running', () => {
      mockUseBackupSettings.mockReturnValue({
        config: {
          enabled: true,
          frequency: 'daily' as const,
          backupPath: '/test/path',
          maxBackups: 10,
        },
        status: {
          isRunning: true,
          nextScheduledBackup: null,
          lastBackupTime: undefined,
          lastBackupSuccess: undefined,
          lastBackupError: undefined,
        },
        toggleEnabled: mockToggleBackupEnabled,
        updateFrequency: mockUpdateBackupFrequency,
        updateBackupPath: mockUpdateBackupPath,
        updateMaxBackups: mockUpdateMaxBackups,
        selectBackupDirectory: mockSelectBackupDirectory,
        performManualBackup: mockPerformManualBackup,
        getStatusSummary: () => 'Creating backup...',
        updateConfig: jest.fn(),
        validateBackupPath: jest.fn(),
        resetConfig: jest.fn(),
        checkBackupDue: jest.fn(),
        performAutomaticBackupIfDue: jest.fn(),
      });

      render(<BackupSettingsSection onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      const backupButton = screen.getByText('Creating Backup...');
      expect(backupButton).toBeDisabled();
    });

    it('should disable backup button when no backup path', () => {
      mockUseBackupSettings.mockReturnValue({
        config: {
          enabled: true,
          frequency: 'daily' as const,
          backupPath: '',
          maxBackups: 10,
        },
        status: {
          isRunning: false,
          nextScheduledBackup: null,
          lastBackupTime: undefined,
          lastBackupSuccess: undefined,
          lastBackupError: undefined,
        },
        toggleEnabled: mockToggleBackupEnabled,
        updateFrequency: mockUpdateBackupFrequency,
        updateBackupPath: mockUpdateBackupPath,
        updateMaxBackups: mockUpdateMaxBackups,
        selectBackupDirectory: mockSelectBackupDirectory,
        performManualBackup: mockPerformManualBackup,
        getStatusSummary: () => 'No backup path selected',
        updateConfig: jest.fn(),
        validateBackupPath: jest.fn(),
        resetConfig: jest.fn(),
        checkBackupDue: jest.fn(),
        performAutomaticBackupIfDue: jest.fn(),
      });

      render(<BackupSettingsSection onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      const backupButton = screen.getByText('Backup Now');
      expect(backupButton).toBeDisabled();
    });
  });

  describe('Error Handling', () => {
    it('should handle selectBackupDirectory errors', async () => {
      const user = userEvent.setup();
      mockSelectBackupDirectory.mockRejectedValue(new Error('Directory selection failed'));
      
      // Set enabled state
      mockUseBackupSettings.mockReturnValue({
        config: {
          enabled: true,
          frequency: 'daily' as const,
          backupPath: '/test/path',
          maxBackups: 10,
        },
        status: {
          isRunning: false,
          nextScheduledBackup: null,
          lastBackupTime: undefined,
          lastBackupSuccess: undefined,
          lastBackupError: undefined,
        },
        toggleEnabled: mockToggleBackupEnabled,
        updateFrequency: mockUpdateBackupFrequency,
        updateBackupPath: mockUpdateBackupPath,
        updateMaxBackups: mockUpdateMaxBackups,
        selectBackupDirectory: mockSelectBackupDirectory,
        performManualBackup: mockPerformManualBackup,
        getStatusSummary: () => 'Enabled',
        updateConfig: jest.fn(),
        validateBackupPath: jest.fn(),
        resetConfig: jest.fn(),
        checkBackupDue: jest.fn(),
        performAutomaticBackupIfDue: jest.fn(),
      });

      render(<BackupSettingsSection onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      const browseButton = screen.getByText('Browse');
      await user.click(browseButton);

      await waitFor(() => {
        expect(mockOnError).toHaveBeenCalledWith('Failed to select backup directory. Please try again.');
      });
    });

    it('should handle performManualBackup errors', async () => {
      const user = userEvent.setup();
      mockPerformManualBackup.mockResolvedValue({ success: false, error: 'Backup failed' });
      
      // Set enabled state
      mockUseBackupSettings.mockReturnValue({
        config: {
          enabled: true,
          frequency: 'daily' as const,
          backupPath: '/test/path',
          maxBackups: 10,
        },
        status: {
          isRunning: false,
          nextScheduledBackup: null,
          lastBackupTime: undefined,
          lastBackupSuccess: undefined,
          lastBackupError: undefined,
        },
        toggleEnabled: mockToggleBackupEnabled,
        updateFrequency: mockUpdateBackupFrequency,
        updateBackupPath: mockUpdateBackupPath,
        updateMaxBackups: mockUpdateMaxBackups,
        selectBackupDirectory: mockSelectBackupDirectory,
        performManualBackup: mockPerformManualBackup,
        getStatusSummary: () => 'Enabled',
        updateConfig: jest.fn(),
        validateBackupPath: jest.fn(),
        resetConfig: jest.fn(),
        checkBackupDue: jest.fn(),
        performAutomaticBackupIfDue: jest.fn(),
      });

      render(<BackupSettingsSection onSuccess={mockOnSuccess} onError={mockOnError} />, { wrapper: TestWrapper });

      const backupButton = screen.getByText('Backup Now');
      await user.click(backupButton);

      await waitFor(() => {
        expect(mockOnError).toHaveBeenCalledWith('Backup failed');
      });
    });
  });
});
